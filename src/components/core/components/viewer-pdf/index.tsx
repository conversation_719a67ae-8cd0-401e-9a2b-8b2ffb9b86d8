import { useRef, useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import type { DocumentProps } from 'react-pdf';
import { cn } from '@/components/core/class-config';
import classConfig from '@/components/core/components/viewer-pdf/class-config.ts';

// 设置 worker
pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/build/pdf.worker.min.mjs',
  import.meta.url,
).toString();

export type ViewerPdfProps = {
  /** 自定义类名 */
  className?: string;
  /** 自定义页类名 */
  pageClassName?: string;
} & Pick<DocumentProps, 'file' | 'loading' | 'error'>;

const ViewerPdf = (props: ViewerPdfProps) => {
  const {
    file,
    error = '加载PDF失败',
    loading = '加载中...',
    className,
    pageClassName,
  } = props;
  const [numPages, setNumPages] = useState<number>(0);
  const documentRef = useRef<HTMLDivElement>(null);

  const onDocumentLoadSuccess: Required<DocumentProps>['onLoadSuccess'] = (
    document,
  ) => {
    const { numPages } = document;
    setNumPages(numPages);
  };

  const onDocumentLoadError = (error: Error) => {
    console.error('Error loading PDF:', error);
  };

  return (
    <Document
      className={cn(classConfig.containerConfig, className)}
      inputRef={documentRef}
      file={file}
      onLoadSuccess={onDocumentLoadSuccess}
      onLoadError={onDocumentLoadError}
      loading={loading}
      error={error}
    >
      {Array.from(new Array(numPages), (_, index) => (
        <Page
          key={`page_${index + 1}`}
          className={cn(classConfig.pageConfig, pageClassName)}
          pageNumber={index + 1}
          width={documentRef.current?.clientWidth}
          renderAnnotationLayer={false}
          renderTextLayer={false}
          loading={loading}
        />
      ))}
    </Document>
  );
};

export default ViewerPdf;
