import { render, screen, waitFor } from '@testing-library/react';
import { describe, expect, test, vi, beforeEach, afterEach } from 'vitest';

// Mock react-pdf before importing the component
vi.mock('react-pdf', () => {
  const mockDocument = vi.fn();
  const mockPage = vi.fn();

  return {
    Document: mockDocument,
    Page: mockPage,
    pdfjs: {
      GlobalWorkerOptions: {
        workerSrc: '',
      },
    },
  };
});

import ViewerPdf from '@/components/core/components/viewer-pdf';

describe('ViewerPdf', () => {
  let mockDocument: any;
  let mockPage: any;

  beforeEach(async () => {
    vi.clearAllMocks();

    // Get the mocked functions
    const reactPdf = await vi.importMock('react-pdf');
    mockDocument = reactPdf.Document;
    mockPage = reactPdf.Page;

    // Mock Document component
    mockDocument.mockImplementation(({ children, onLoadSuccess, loading, error, className }) => {
      // Simulate successful load
      if (onLoadSuccess) {
        setTimeout(() => {
          onLoadSuccess({ numPages: 3 });
        }, 0);
      }

      return (
        <div className={className} data-testid="pdf-document">
          {loading && <div data-testid="loading">{loading}</div>}
          {error && <div data-testid="error">{error}</div>}
          {children}
        </div>
      );
    });

    // Mock Page component
    mockPage.mockImplementation(({ pageNumber, className, loading }) => (
      <div className={className} data-testid={`pdf-page-${pageNumber}`}>
        {loading && <div data-testid={`page-loading-${pageNumber}`}>{loading}</div>}
        Page {pageNumber}
      </div>
    ));
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders basic ViewerPdf correctly', async () => {
    const testFile = 'test.pdf';
    
    render(<ViewerPdf file={testFile} />);
    
    // Check if Document component is rendered
    expect(screen.getByTestId('pdf-document')).toBeInTheDocument();
    
    // Wait for pages to be rendered after onLoadSuccess
    await waitFor(() => {
      expect(screen.getByTestId('pdf-page-1')).toBeInTheDocument();
      expect(screen.getByTestId('pdf-page-2')).toBeInTheDocument();
      expect(screen.getByTestId('pdf-page-3')).toBeInTheDocument();
    });
  });

  test('renders with custom className and pageClassName', async () => {
    const testFile = 'test.pdf';
    const customClassName = 'custom-container';
    const customPageClassName = 'custom-page';
    
    render(
      <ViewerPdf 
        file={testFile} 
        className={customClassName}
        pageClassName={customPageClassName}
      />
    );
    
    const document = screen.getByTestId('pdf-document');
    expect(document).toHaveClass(customClassName);
    
    await waitFor(() => {
      const page1 = screen.getByTestId('pdf-page-1');
      expect(page1).toHaveClass(customPageClassName);
    });
  });

  test('displays custom loading message', () => {
    const testFile = 'test.pdf';
    const customLoading = 'Custom loading...';
    
    render(<ViewerPdf file={testFile} loading={customLoading} />);
    
    expect(screen.getByTestId('loading')).toHaveTextContent(customLoading);
  });

  test('displays custom error message', async () => {
    const testFile = 'test.pdf';
    const customError = 'Custom error message';

    // Get the mocked functions
    const reactPdf = await vi.importMock('react-pdf');
    const mockDoc = reactPdf.Document;

    // Mock Document to not call onLoadSuccess
    mockDoc.mockImplementation(({ children, loading, error, className }) => (
      <div className={className} data-testid="pdf-document">
        {loading && <div data-testid="loading">{loading}</div>}
        {error && <div data-testid="error">{error}</div>}
        {children}
      </div>
    ));

    render(<ViewerPdf file={testFile} error={customError} />);

    expect(screen.getByTestId('error')).toHaveTextContent(customError);
  });

  test('displays default loading and error messages', () => {
    const testFile = 'test.pdf';
    
    render(<ViewerPdf file={testFile} />);
    
    expect(screen.getByTestId('loading')).toHaveTextContent('加载中...');
    expect(screen.getByTestId('error')).toHaveTextContent('加载PDF失败');
  });

  test('handles document load success correctly', async () => {
    const testFile = 'test.pdf';
    
    render(<ViewerPdf file={testFile} />);
    
    // Initially no pages should be rendered
    expect(screen.queryByTestId('pdf-page-1')).not.toBeInTheDocument();
    
    // Wait for onLoadSuccess to be called and pages to be rendered
    await waitFor(() => {
      expect(screen.getByTestId('pdf-page-1')).toBeInTheDocument();
      expect(screen.getByTestId('pdf-page-2')).toBeInTheDocument();
      expect(screen.getByTestId('pdf-page-3')).toBeInTheDocument();
    });
  });

  test('handles document load error correctly', async () => {
    const testFile = 'test.pdf';
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    // Get the mocked functions
    const reactPdf = await vi.importMock('react-pdf');
    const mockDoc = reactPdf.Document;

    // Mock Document to call onLoadError
    mockDoc.mockImplementation(({ onLoadError, className }) => {
      if (onLoadError) {
        setTimeout(() => {
          onLoadError(new Error('PDF load failed'));
        }, 0);
      }

      return <div className={className} data-testid="pdf-document" />;
    });

    render(<ViewerPdf file={testFile} />);

    // Wait for error to be logged
    await waitFor(() => {
      expect(consoleErrorSpy).toHaveBeenCalledWith('Error loading PDF:', expect.any(Error));
    });

    consoleErrorSpy.mockRestore();
  });

  test('passes correct props to Document component', async () => {
    const testFile = 'test.pdf';
    const customLoading = 'Loading PDF...';
    const customError = 'Failed to load PDF';
    const customClassName = 'pdf-container';

    // Get the mocked functions
    const reactPdf = await vi.importMock('react-pdf');
    const mockDoc = reactPdf.Document;

    render(
      <ViewerPdf
        file={testFile}
        loading={customLoading}
        error={customError}
        className={customClassName}
      />
    );

    expect(mockDoc).toHaveBeenCalledWith(
      expect.objectContaining({
        file: testFile,
        loading: customLoading,
        error: customError,
        className: expect.stringContaining(customClassName),
        onLoadSuccess: expect.any(Function),
        onLoadError: expect.any(Function),
        inputRef: expect.any(Object),
      }),
      expect.anything()
    );
  });

  test('passes correct props to Page components', async () => {
    const testFile = 'test.pdf';
    const customPageClassName = 'pdf-page';
    const customLoading = 'Loading page...';

    // Get the mocked functions
    const reactPdf = await vi.importMock('react-pdf');
    const mockPg = reactPdf.Page;

    render(
      <ViewerPdf
        file={testFile}
        pageClassName={customPageClassName}
        loading={customLoading}
      />
    );

    await waitFor(() => {
      expect(mockPg).toHaveBeenCalledWith(
        expect.objectContaining({
          pageNumber: 1,
          className: expect.stringContaining(customPageClassName),
          loading: customLoading,
          renderAnnotationLayer: false,
          renderTextLayer: false,
          width: undefined, // Initially undefined since ref.current?.clientWidth is undefined
        }),
        expect.anything()
      );
    });
  });

  test('renders correct number of pages based on numPages', async () => {
    const testFile = 'test.pdf';

    // Get the mocked functions
    const reactPdf = await vi.importMock('react-pdf');
    const mockDoc = reactPdf.Document;

    // Mock Document to return different numPages
    mockDoc.mockImplementation(({ children, onLoadSuccess, className }) => {
      if (onLoadSuccess) {
        setTimeout(() => {
          onLoadSuccess({ numPages: 5 });
        }, 0);
      }

      return (
        <div className={className} data-testid="pdf-document">
          {children}
        </div>
      );
    });

    render(<ViewerPdf file={testFile} />);

    await waitFor(() => {
      expect(screen.getByTestId('pdf-page-1')).toBeInTheDocument();
      expect(screen.getByTestId('pdf-page-2')).toBeInTheDocument();
      expect(screen.getByTestId('pdf-page-3')).toBeInTheDocument();
      expect(screen.getByTestId('pdf-page-4')).toBeInTheDocument();
      expect(screen.getByTestId('pdf-page-5')).toBeInTheDocument();
    });
  });

  test('applies default class configuration', () => {
    const testFile = 'test.pdf';
    
    render(<ViewerPdf file={testFile} />);
    
    const document = screen.getByTestId('pdf-document');
    expect(document).toHaveClass('text-center', 'bg-white');
  });

  test('handles zero pages correctly', async () => {
    const testFile = 'test.pdf';

    // Get the mocked functions
    const reactPdf = await vi.importMock('react-pdf');
    const mockDoc = reactPdf.Document;

    // Mock Document to return 0 pages
    mockDoc.mockImplementation(({ children, onLoadSuccess, className }) => {
      if (onLoadSuccess) {
        setTimeout(() => {
          onLoadSuccess({ numPages: 0 });
        }, 0);
      }

      return (
        <div className={className} data-testid="pdf-document">
          {children}
        </div>
      );
    });

    render(<ViewerPdf file={testFile} />);

    await waitFor(() => {
      // Should not render any pages
      expect(screen.queryByTestId('pdf-page-1')).not.toBeInTheDocument();
    });
  });
});
