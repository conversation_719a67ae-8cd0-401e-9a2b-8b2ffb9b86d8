{"name": "react-ts-template", "version": "1.0.0", "description": "一套基于react19、ts、vite6的项目模板,帮助快速搭建react项目", "keywords": ["react19 ts template", "react template", "react", "template", "vite"], "homepage": "https://github.com/liangskyli/react-ts-template", "bugs": "https://github.com/liangskyli/react-ts-template/issues", "repository": {"type": "git", "url": "https://github.com/liangskyli/react-ts-template"}, "license": "MIT", "author": "lian<PERSON>ky <<EMAIL>>", "type": "module", "scripts": {"build:dev": "pnpm vite build -m dev", "build:pro": "pnpm vite build -m pro", "build:test": "pnpm vite build -m test", "coverage": "vitest run --coverage", "deps:check": "pnpm outdated -r", "dev": "vite -m dev", "dev:mock": "vite -m dev-mock", "dev:mock-server": "pnpm mock-server -c mock.config.ts", "dev:pro": "vite -m pro", "dev:test": "vite -m test", "preinstall": "npx only-allow pnpm", "lint:all": "pnpm run lint:eslint && pnpm run lint:style && pnpm run lint:format", "lint:eslint": "eslint --max-warnings 0  \"**/*.{ts,tsx,js,jsx,cjs,mjs}\" --fix", "lint:format": "prettier --write \"**/*.{ts,tsx,js,jsx,cjs,mjs,html,less,scss,css,json}\"", "lint:style": "stylelint \"**/*.{css,less,scss}\" --fix", "prepare": "husky", "preview:dev": "pnpm vite preview -m dev", "preview:pro": "pnpm vite preview -m pro", "preview:test": "pnpm vite preview -m test", "test": "vitest run", "test-u": "vitest -u run", "typecheck": "tsc --noEmit", "update:deps": "pnpm update --interactive --latest"}, "dependencies": {"@floating-ui/react": "^0.27.9", "@headlessui/react": "^2.2.4", "@liangskyli/axios-request": "^0.2.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "immer": "^10.1.1", "lru-cache": "^10.4.3", "optics-ts": "^2.4.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-pdf": "^10.0.1", "react-router": "^7.6.1", "react-virtualized": "^9.22.6", "tailwind-merge": "^2.2.1", "zustand": "^5.0.5"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@commitlint/prompt-cli": "^19.8.1", "@eslint/js": "^9.27.0", "@liangskyli/mock": "^5.0.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/node": "^22.15.23", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@types/react-virtualized": "^9.22.2", "@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-react-swc": "^3.10.0", "@vitest/coverage-v8": "^3.1.4", "autoprefixer": "^10.4.21", "eslint": "^9.27.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "less": "^4.3.0", "lint-staged": "^15.5.2", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-packagejson": "^2.5.14", "prettier-plugin-tailwindcss": "^0.6.11", "react-dev-inspector": "^2.0.1", "sass": "^1.89.0", "stylelint": "^16.19.1", "stylelint-config-recess-order": "^6.0.0", "stylelint-config-standard": "^38.0.0", "stylelint-config-standard-less": "^3.0.1", "stylelint-config-standard-scss": "^14.0.0", "stylelint-order": "^6.0.4", "tailwindcss": "^3.4.17", "terser": "^5.40.0", "typescript": "^5.8.3", "typescript-eslint": "^8.33.0", "vite": "^6.3.5", "vite-plugin-checker": "^0.9.3", "vite-plugin-svgr": "^4.3.0", "vitest": "^3.1.4"}, "packageManager": "pnpm@10.11.0", "engines": {"node": "^18.20.0 || ^20.0.0 || >=22.0.0", "pnpm": "^10"}}